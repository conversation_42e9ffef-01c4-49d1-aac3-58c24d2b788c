#include "actuator.h"

Actuator::Actuator(int pin) : ledPin(pin), ledState(false) {
}

void Actuator::initialize() {
  pinMode(ledPin, OUTPUT);
  digitalWrite(ledPin, LOW);
  ledState = false;
}

void Actuator::blinkLED(int times, int delayMs) {
  for (int i = 0; i < times; i++) {
    digitalWrite(ledPin, HIGH);
    delay(delayMs);
    digitalWrite(ledPin, LOW);
    delay(delayMs);
  }
}

void Actuator::setLED(bool state) {
  ledState = state;
  digitalWrite(ledPin, state ? HIGH : LOW);
}

void Actuator::toggleLED() {
  ledState = !ledState;
  digitalWrite(ledPin, ledState ? HIGH : LOW);
}

void Actuator::indicateAuthSuccess() {
  // 认证成功：快速闪烁3次
  blinkLED(3, 100);
}

void Actuator::indicateAuthFailure() {
  // 认证失败：慢速闪烁2次
  blinkLED(2, 300);
}

void Actuator::indicateRegistrationSuccess() {
  // 注册成功：长亮1秒后快速闪烁5次
  setLED(true);
  delay(1000);
  setLED(false);
  delay(200);
  blinkLED(5, 100);
}

void Actuator::indicateRegistrationFailure() {
  // 注册失败：快速闪烁10次
  blinkLED(10, 50);
}

void Actuator::indicateSystemStart() {
  // 系统启动：渐进式闪烁
  for (int i = 0; i < 3; i++) {
    blinkLED(1, 100);
    delay(100);
  }
}

void Actuator::indicateRegistrationMode() {
  // 进入注册模式：长亮500ms后关闭
  setLED(true);
  delay(500);
  setLED(false);
}

#include "authentication.h"
#include "card_manager.h"

// 全局实例声明
extern CardManager cardManager;

Authentication::Authentication(Adafruit_PN532* nfcInstance) : nfc(nfcInstance) {
}

bool Authentication::initialize() {
  nfc->begin();
  uint32_t version = nfc->getFirmwareVersion();
  if (!version) {
    Serial.println("PN532 not found");
    return false;
  }
  
  Serial.print("Found PN532 with firmware version: 0x");
  Serial.println(version, HEX);
  
  nfc->SAMConfig();
  return true;
}

bool Authentication::startPassiveDetection() {
  Serial.println("Starting passive target detection...");
  return nfc->startPassiveTargetIDDetection(PN532_MIFARE_ISO14443A);
}

AuthResult Authentication::readDetectedCard() {
  AuthResult result;
  result.success = false;
  
  uint8_t uid[7];
  uint8_t uidLen;
  
  // 读取已检测到的卡片信息
  if (nfc->readDetectedPassiveTargetID(uid, &uidLen)) {
    result.uid = uidToString(uid, uidLen);
    result.success = true;
    result.message = "Card read successful";
    
    Serial.print("Card UID: ");
    Serial.println(result.uid);
  } else {
    result.message = "Failed to read card data";
    Serial.println(result.message);
  }
  
  return result;
}

AuthResult Authentication::authenticateCard() {
  AuthResult result;
  result.success = false;
  
  uint8_t uid[7];
  uint8_t uidLen;
  
  // 读取已检测到的卡片
  if (!nfc->readDetectedPassiveTargetID(uid, &uidLen)) {
    result.message = "Failed to read card";
    return result;
  }
  
  result.uid = uidToString(uid, uidLen);
  
  // 在数据库中查找卡片
  String keyHex;
  if (!cardManager.findCardByUID(result.uid, keyHex)) {
    result.message = "Card not registered";
    return result;
  }
  
  // 获取存储的密钥并进行认证
  uint8_t key[KEY_SIZE];
  hexStringToKey(keyHex, key);
  
  if (authenticateBlock(uid, uidLen, AUTH_BLOCK, key)) {
    result.success = true;
    result.message = "Authentication successful";
  } else {
    result.message = "Authentication failed";
  }
  
  return result;
}

AuthResult Authentication::registerCard() {
  AuthResult result;
  result.success = false;
  
  uint8_t uid[7];
  uint8_t uidLen;
  
  // 读取已检测到的卡片
  if (!nfc->readDetectedPassiveTargetID(uid, &uidLen)) {
    result.message = "Failed to read card";
    return result;
  }
  
  result.uid = uidToString(uid, uidLen);
  
  // 检查卡片是否已注册
  if (cardManager.isCardRegistered(result.uid)) {
    result.message = "Card already registered";
    return result;
  }
  
  // 使用默认密钥进行认证
  if (!authenticateBlock(uid, uidLen, SECTOR_TRAILER_BLOCK, defaultKey)) {
    result.message = "Authentication with default key failed";
    return result;
  }
  
  // 生成并写入新密钥
  uint8_t newKey[KEY_SIZE];
  generateRandomKey(newKey);
  
  if (!writeSectorTrailer(newKey)) {
    result.message = "Failed to write sector trailer";
    return result;
  }
  
  // 添加到数据库
  String keyHex = keyToHexString(newKey);
  if (cardManager.addCard(result.uid, keyHex)) {
    result.success = true;
    result.message = "Card registered successfully";
  } else {
    result.message = "Failed to save card to database";
  }
  
  return result;
}

String Authentication::uidToString(uint8_t* uid, uint8_t len) {
  String result;
  for (uint8_t i = 0; i < len; i++) {
    if (uid[i] < 0x10) result += "0";
    result += String(uid[i], HEX);
  }
  result.toUpperCase();
  return result;
}

void Authentication::generateRandomKey(uint8_t* key) {
  for (int i = 0; i < KEY_SIZE; i++) {
    key[i] = random(0, 256);
  }
}

String Authentication::keyToHexString(uint8_t* key) {
  char keyHex[13] = {0};
  for (int i = 0; i < KEY_SIZE; i++) {
    sprintf(&keyHex[i * 2], "%02X", key[i]);
  }
  return String(keyHex);
}

void Authentication::hexStringToKey(const String& hexString, uint8_t* key) {
  for (int i = 0; i < KEY_SIZE; i++) {
    key[i] = strtoul(hexString.substring(i * 2, i * 2 + 2).c_str(), NULL, 16);
  }
}

bool Authentication::readCardUID(uint8_t* uid, uint8_t* uidLen) {
  return nfc->readPassiveTargetID(PN532_MIFARE_ISO14443A, uid, uidLen);
}

bool Authentication::authenticateBlock(uint8_t* uid, uint8_t uidLen, uint8_t blockNumber, uint8_t* key) {
  return nfc->mifareclassic_AuthenticateBlock(uid, uidLen, blockNumber, 0, key);
}

bool Authentication::writeSectorTrailer(uint8_t* newKey) {
  uint8_t trailer[TRAILER_SIZE];
  
  // 设置新的KeyA
  memcpy(trailer, newKey, KEY_SIZE);
  
  // 设置默认访问位
  memcpy(trailer + 6, "\xFF\x07\x80\x69", 4);
  
  // 保持默认KeyB
  memcpy(trailer + 10, defaultKey, KEY_SIZE);
  
  return nfc->mifareclassic_WriteDataBlock(SECTOR_TRAILER_BLOCK, trailer);
}

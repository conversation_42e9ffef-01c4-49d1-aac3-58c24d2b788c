// Door Access System using ESP32, PN532, and MIFARE Classic
// Features:
// 1. Register blank card: generate random key, write to sector trailer (block 7), record mapping in SPIFFS JSON
// 2. Automatic card authentication with IRQ-based detection and cooldown logic
// 3. List and delete cards: manage registered cards via Serial commands

#include <Arduino.h>
#include <Wire.h>
#include <SPI.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include <Adafruit_PN532.h>
#include "authentication.h"
#include "actuator.h"
#include "state_machine.h"
#include "card_manager.h"

// =============================================================================
// 配置
// =============================================================================
// PN532 I2C pins
#define PN532_IRQ   34
#define PN532_RESET 5

// LED pin
#define LED_PIN 2

// =============================================================================
// 全局变量
// =============================================================================
Adafruit_PN532 nfc(PN532_IRQ, PN532_RESET);

// 全局模块实例
Authentication auth(&nfc);
Actuator actuator(LED_PIN);
CardManager cardManager;
StateMachine stateMachine;

// =============================================================================
// 命令处理 - 简化版本，移除auth命令
// =============================================================================

void handleRegisterCommand() {
  stateMachine.enterRegistrationMode();
}

void handleListCommand() {
  cardManager.listAllCards();
}

void handleDeleteCommand(const String& uid) {
  if (uid.length() == 0) {
    Serial.println("Usage: del <UID>");
    return;
  }

  if (cardManager.removeCard(uid)) {
    Serial.println("Deleted " + uid);
  } else {
    Serial.println("Card not found: " + uid);
  }
}

void handleClearCommand() {
  cardManager.clearAllCards();
}

void processSerialCommand() {
  String command = Serial.readStringUntil('\n');
  command.trim();

  if (command.equalsIgnoreCase("reg")) {
    handleRegisterCommand();
  }
  else if (command.equalsIgnoreCase("list")) {
    handleListCommand();
  }
  else if (command.startsWith("del")) {
    String uid = command.substring(3);
    uid.trim();
    handleDeleteCommand(uid);
  }
  else if (command.equalsIgnoreCase("clear")) {
    handleClearCommand();
  }
  else {
    Serial.println("Unknown command");
    Serial.println("Available commands: reg, list, del <UID>, clear");
    Serial.println("Note: Authentication is now automatic when cards are detected");
  }
}

// =============================================================================
// 初始化
// =============================================================================
bool initializeSystem() {
  // 初始化执行器
  actuator.initialize();

  // 初始化卡片管理器
  if (!cardManager.initialize()) {
    Serial.println("Failed to initialize card manager");
    return false;
  }

  // 初始化认证模块
  if (!auth.initialize()) {
    Serial.println("Failed to initialize NFC authentication");
    return false;
  }

  return true;
}

void printWelcomeMessage() {
  Serial.println("=================================");
  Serial.println("    Door Access System Ready    ");
  Serial.println("=================================");
  Serial.println("Commands:");
  Serial.println("  reg       - Register new card");
  Serial.println("  list      - List all cards");
  Serial.println("  del <UID> - Delete card");
  Serial.println("  clear     - Clear all cards");
  Serial.println("=================================");
  Serial.println("Authentication is automatic!");
  Serial.println("Just tap your card to authenticate.");
  Serial.println("=================================");
}

// =============================================================================
// 主函数
// =============================================================================
void setup() {
  Serial.begin(115200);

  // 系统启动指示
  actuator.indicateSystemStart();

  // 初始化所有模块
  if (!initializeSystem()) {
    Serial.println("System initialization failed!");
    while (true) delay(1000);
  }

  // 启动状态机，开始检测卡片
  stateMachine.startDetection();

  printWelcomeMessage();
}

void loop() {
  // 处理串口命令
  if (Serial.available()) {
    processSerialCommand();
  }

  // 更新状态机 - 这里处理自动认证逻辑
  stateMachine.update();

  // 小延迟以防止过度占用CPU
  delay(10);
}

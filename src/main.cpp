// Door Access System using ESP32, PN532, and MIFARE Classic
// Features:
// 1. Register blank card: generate random key, write to sector trailer (block 7), record mapping in SPIFFS JSON
// 2. Authenticate card: read UID, lookup key, perform Crypto1 authentication on sector1 block4, blink LED on success
// 3. List and delete cards: manage registered cards via Serial commands
// 4. IRQ-based cooldown system: prevents repeated authentication of same card while allowing instant card switching

#include <Arduino.h>
#include <Wire.h>
#include <SPI.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include <Adafruit_PN532.h>

// =============================================================================
// 配置
// =============================================================================
// PN532 I2C pins
#define PN532_IRQ   34
#define PN532_RESET 5

// LED pin
#define LED_PIN 2

// MIFARE Classic settings
#define SECTOR_TRAILER_BLOCK 7
#define AUTH_BLOCK 4
#define KEY_SIZE 6
#define TRAILER_SIZE 16

// Cooldown settings
#define COOLDOWN_PERIOD 1000  // 1 second cooldown
#define CARD_DETECT_DELAY 100 // 100ms delay for same card re-detection

// 文件系统
const char* CARD_FILE = "/cards.json";

// =============================================================================
// 状态管理
// =============================================================================
enum SystemState {
  STATE_IDLE,           // 等待卡片
  STATE_COOLDOWN,       // 冷却期
  STATE_MANUAL_MODE     // 手动命令模式
};

struct CooldownState {
  bool active;
  unsigned long startTime;
  uint8_t lastUID[7];
  uint8_t lastUIDLength;
  bool hasLastUID;
};

// =============================================================================
// 全局变量
// =============================================================================
Adafruit_PN532 nfc(PN532_IRQ, PN532_RESET);
StaticJsonDocument<1024> cardDatabase;
uint8_t defaultKey[KEY_SIZE] = { 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF };

SystemState currentState = STATE_IDLE;
CooldownState cooldown = {false, 0, {0}, 0, false};
int irqCurrent, irqPrevious;
bool nfcListening = false;

// =============================================================================
// 前置声明
// =============================================================================
void startNFCListening();
void handleCardAuthentication();
void enterCooldownMode(uint8_t* uid, uint8_t uidLen);
void exitCooldownMode();
bool compareUID(uint8_t* uid1, uint8_t len1, uint8_t* uid2, uint8_t len2);

// =============================================================================
// 工具函数
// =============================================================================
String uidToString(uint8_t* uid, uint8_t len) {
  String result;
  for (uint8_t i = 0; i < len; i++) {
    if (uid[i] < 0x10) result += "0";
    result += String(uid[i], HEX);
  }
  result.toUpperCase();
  return result;
}

void generateRandomKey(uint8_t* key) {
  for (int i = 0; i < KEY_SIZE; i++) {
    key[i] = random(0, 256);
  }
}

String keyToHexString(uint8_t* key) {
  char keyHex[13] = {0};
  for (int i = 0; i < KEY_SIZE; i++) {
    sprintf(&keyHex[i * 2], "%02X", key[i]);
  }
  return String(keyHex);
}

void hexStringToKey(const String& hexString, uint8_t* key) {
  for (int i = 0; i < KEY_SIZE; i++) {
    key[i] = strtoul(hexString.substring(i * 2, i * 2 + 2).c_str(), NULL, 16);
  }
}

bool compareUID(uint8_t* uid1, uint8_t len1, uint8_t* uid2, uint8_t len2) {
  if (len1 != len2) return false;
  return memcmp(uid1, uid2, len1) == 0;
}

// =============================================================================
// LED 控制
// =============================================================================
void blinkLED(int times = 3, int delayMs = 100) {
  for (int i = 0; i < times; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(delayMs);
    digitalWrite(LED_PIN, LOW);
    delay(delayMs);
  }
}

// =============================================================================
// 文件系统操作
// =============================================================================
void saveCards() {
  File file = SPIFFS.open(CARD_FILE, FILE_WRITE);
  if (!file) {
    Serial.println("Failed to open card file for writing");
    return;
  }
  serializeJsonPretty(cardDatabase, file);
  file.close();
}

void loadCards() {
  File file = SPIFFS.open(CARD_FILE, FILE_READ);
  if (!file) {
    cardDatabase.to<JsonArray>();
    saveCards();
  } else {
    DeserializationError err = deserializeJson(cardDatabase, file);
    if (err) {
      Serial.println("Card database corrupt, resetting...");
      cardDatabase.to<JsonArray>();
      saveCards();
    }
    file.close();
  }
}

// =============================================================================
// NFC 操作
// =============================================================================
bool readCardUID(uint8_t* uid, uint8_t* uidLen) {
  return nfc.readPassiveTargetID(PN532_MIFARE_ISO14443A, uid, uidLen);
}

bool authenticateBlock(uint8_t* uid, uint8_t uidLen, uint8_t blockNumber, uint8_t* key) {
  return nfc.mifareclassic_AuthenticateBlock(uid, uidLen, blockNumber, 0, key);
}

bool writeSectorTrailer(uint8_t* newKey) {
  uint8_t trailer[TRAILER_SIZE];
  memcpy(trailer, newKey, KEY_SIZE);
  memcpy(trailer + 6, "\xFF\x07\x80\x69", 4);
  memcpy(trailer + 10, defaultKey, KEY_SIZE);
  return nfc.mifareclassic_WriteDataBlock(SECTOR_TRAILER_BLOCK, trailer);
}

void startNFCListening() {
  irqPrevious = irqCurrent = HIGH;
  nfcListening = true;

  Serial.println("Starting NFC detection...");
  if (!nfc.startPassiveTargetIDDetection(PN532_MIFARE_ISO14443A)) {
    Serial.println("Failed to start NFC detection");
    nfcListening = false;
  }
}

// =============================================================================
// 卡片管理
// =============================================================================
bool findCardByUID(const String& uid, String& keyHex) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  for (JsonObject card : cards) {
    if (card["uid"] == uid) {
      keyHex = card["key"].as<String>();
      return true;
    }
  }
  return false;
}

bool isCardRegistered(const String& uid) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  for (JsonObject card : cards) {
    if (card["uid"] == uid) {
      return true;
    }
  }
  return false;
}

bool addCardToDatabase(const String& uid, const String& keyHex) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  JsonObject newCard = cards.createNestedObject();
  newCard["uid"] = uid;
  newCard["key"] = keyHex;
  saveCards();
  return true;
}

bool removeCardFromDatabase(const String& uid) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  for (size_t i = 0; i < cards.size(); i++) {
    if (cards[i]["uid"] == uid) {
      cards.remove(i);
      saveCards();
      return true;
    }
  }
  return false;
}

// =============================================================================
// 冷却期管理
// =============================================================================
void enterCooldownMode(uint8_t* uid, uint8_t uidLen) {
  cooldown.active = true;
  cooldown.startTime = millis();
  cooldown.hasLastUID = true;
  cooldown.lastUIDLength = uidLen;
  memcpy(cooldown.lastUID, uid, uidLen);

  currentState = STATE_COOLDOWN;

  // 立即发起新的检测
  startNFCListening();

  Serial.println("Entered cooldown mode");
}

void exitCooldownMode() {
  cooldown.active = false;
  cooldown.hasLastUID = false;
  currentState = STATE_IDLE;
  Serial.println("Exited cooldown mode");
}

void handleCooldownIRQ() {
  uint8_t uid[7], uidLen;

  // 读取卡片信息
  if (!nfc.readDetectedPassiveTargetID(uid, &uidLen)) {
    Serial.println("Failed to read card in cooldown");
    // 延迟后重新开始监听
    delay(CARD_DETECT_DELAY);
    startNFCListening();
    return;
  }

  // 比较UID
  if (cooldown.hasLastUID && compareUID(uid, uidLen, cooldown.lastUID, cooldown.lastUIDLength)) {
    // 同一张卡，延迟后重新监听，重置冷却计时器
    Serial.println("Same card detected in cooldown, resetting timer");
    cooldown.startTime = millis();
    delay(CARD_DETECT_DELAY);
    startNFCListening();
  } else {
    // 不同的卡，立即退出冷却期进行鉴权
    Serial.println("Different card detected, exiting cooldown");
    exitCooldownMode();

    // 直接进行鉴权处理
    String uidString = uidToString(uid, uidLen);
    Serial.print("New card UID: ");
    Serial.println(uidString);

    performAuthentication(uid, uidLen, uidString);
  }
}

// =============================================================================
// 鉴权处理
// =============================================================================
void performAuthentication(uint8_t* uid, uint8_t uidLen, const String& uidString) {
  String keyHex;
  if (!findCardByUID(uidString, keyHex)) {
    Serial.println("Card not registered");
    enterCooldownMode(uid, uidLen);
    return;
  }

  uint8_t key[KEY_SIZE];
  hexStringToKey(keyHex, key);

  if (authenticateBlock(uid, uidLen, AUTH_BLOCK, key)) {
    Serial.println("Authentication successful");
    blinkLED();
  } else {
    Serial.println("Authentication failed");
  }

  enterCooldownMode(uid, uidLen);
}

void handleCardAuthentication() {
  uint8_t uid[7], uidLen;

  if (!nfc.readDetectedPassiveTargetID(uid, &uidLen)) {
    Serial.println("Failed to read card");
    startNFCListening();
    return;
  }

  String uidString = uidToString(uid, uidLen);
  Serial.print("Card detected - UID: ");
  Serial.println(uidString);

  performAuthentication(uid, uidLen, uidString);
}

// =============================================================================
// 手动命令处理
// =============================================================================
void handleRegisterCommand() {
  currentState = STATE_MANUAL_MODE;
  Serial.println("-- Tap blank card to register --");

  uint8_t uid[7], uidLen;
  if (!readCardUID(uid, &uidLen)) {
    Serial.println("No card detected");
    currentState = STATE_IDLE;
    startNFCListening();
    return;
  }

  String uidString = uidToString(uid, uidLen);
  Serial.print("UID: ");
  Serial.println(uidString);

  if (isCardRegistered(uidString)) {
    Serial.println("Card already registered");
    currentState = STATE_IDLE;
    startNFCListening();
    return;
  }

  if (!authenticateBlock(uid, uidLen, SECTOR_TRAILER_BLOCK, defaultKey)) {
    Serial.println("Authentication with default key failed");
    currentState = STATE_IDLE;
    startNFCListening();
    return;
  }

  uint8_t newKey[KEY_SIZE];
  generateRandomKey(newKey);

  if (!writeSectorTrailer(newKey)) {
    Serial.println("Failed to write sector trailer");
    currentState = STATE_IDLE;
    startNFCListening();
    return;
  }

  String keyHex = keyToHexString(newKey);
  if (addCardToDatabase(uidString, keyHex)) {
    Serial.println("Card registered successfully");
  } else {
    Serial.println("Failed to save card to database");
  }

  currentState = STATE_IDLE;
  startNFCListening();
}

void handleManualAuthCommand() {
  currentState = STATE_MANUAL_MODE;
  Serial.println("-- Tap card to authenticate --");

  uint8_t uid[7], uidLen;
  if (!readCardUID(uid, &uidLen)) {
    Serial.println("No card detected");
    currentState = STATE_IDLE;
    startNFCListening();
    return;
  }

  String uidString = uidToString(uid, uidLen);
  Serial.print("UID: ");
  Serial.println(uidString);

  String keyHex;
  if (!findCardByUID(uidString, keyHex)) {
    Serial.println("Card not registered");
    currentState = STATE_IDLE;
    startNFCListening();
    return;
  }

  uint8_t key[KEY_SIZE];
  hexStringToKey(keyHex, key);

  if (authenticateBlock(uid, uidLen, AUTH_BLOCK, key)) {
    Serial.println("Authentication successful");
    blinkLED();
  } else {
    Serial.println("Authentication failed");
  }

  currentState = STATE_IDLE;
  startNFCListening();
}

void handleListCommand() {
  Serial.println("-- Registered Cards --");
  JsonArray cards = cardDatabase.as<JsonArray>();

  if (cards.size() == 0) {
    Serial.println("No cards registered");
    return;
  }

  for (JsonObject card : cards) {
    Serial.print(card["uid"].as<const char*>());
    Serial.print(" : ");
    Serial.println(card["key"].as<const char*>());
  }
}

void handleDeleteCommand(const String& uid) {
  if (uid.length() == 0) {
    Serial.println("Usage: del <UID>");
    return;
  }

  if (removeCardFromDatabase(uid)) {
    Serial.println("Deleted " + uid);

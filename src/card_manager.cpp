#include "card_manager.h"

CardManager::CardManager() {
}

bool CardManager::initialize() {
  if (!SPIFFS.begin(true)) {
    Serial.println("SPIFFS Mount Failed");
    return false;
  }
  loadCards();
  return true;
}

bool CardManager::findCardByUID(const String& uid, String& keyHex) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  for (JsonObject card : cards) {
    if (card["uid"] == uid) {
      keyHex = card["key"].as<String>();
      return true;
    }
  }
  return false;
}

bool CardManager::isCardRegistered(const String& uid) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  for (JsonObject card : cards) {
    if (card["uid"] == uid) {
      return true;
    }
  }
  return false;
}

CardInfo CardManager::getCardInfo(const String& uid) {
  CardInfo info;
  info.valid = false;
  
  JsonArray cards = cardDatabase.as<JsonArray>();
  for (JsonObject card : cards) {
    if (card["uid"] == uid) {
      info.uid = card["uid"].as<String>();
      info.key = card["key"].as<String>();
      info.valid = true;
      break;
    }
  }
  
  return info;
}

bool CardManager::addCard(const String& uid, const String& keyHex) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  JsonObject newCard = cards.createNestedObject();
  newCard["uid"] = uid;
  newCard["key"] = keyHex;
  saveCards();
  return true;
}

bool CardManager::removeCard(const String& uid) {
  JsonArray cards = cardDatabase.as<JsonArray>();
  for (size_t i = 0; i < cards.size(); i++) {
    if (cards[i]["uid"] == uid) {
      cards.remove(i);
      saveCards();
      return true;
    }
  }
  return false;
}

int CardManager::getCardCount() {
  JsonArray cards = cardDatabase.as<JsonArray>();
  return cards.size();
}

void CardManager::listAllCards() {
  Serial.println("-- Registered Cards --");
  JsonArray cards = cardDatabase.as<JsonArray>();
  
  if (cards.size() == 0) {
    Serial.println("No cards registered");
    return;
  }

  for (JsonObject card : cards) {
    Serial.print(card["uid"].as<const char*>());
    Serial.print(" : ");
    Serial.println(card["key"].as<const char*>());
  }
}

bool CardManager::clearAllCards() {
  cardDatabase.to<JsonArray>();
  saveCards();
  Serial.println("All cards cleared");
  return true;
}

void CardManager::saveCards() {
  File file = SPIFFS.open(CARD_FILE, FILE_WRITE);
  if (!file) {
    Serial.println("Failed to open card file for writing");
    return;
  }
  serializeJsonPretty(cardDatabase, file);
  file.close();
}

void CardManager::loadCards() {
  File file = SPIFFS.open(CARD_FILE, FILE_READ);
  if (!file) {
    // 如果文件不存在，创建空数组
    cardDatabase.to<JsonArray>();
    saveCards();
  } else {
    DeserializationError err = deserializeJson(cardDatabase, file);
    if (err) {
      // 如果文件损坏，重置
      Serial.println("Card database corrupt, resetting...");
      cardDatabase.to<JsonArray>();
      saveCards();
    }
    file.close();
  }
}

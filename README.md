# NFC门禁系统 - 重构版本

基于ESP32和PN532的智能门禁系统，采用IRQ-based检测和状态机架构，实现自动认证和智能冷却期管理。

## 主要特性

✅ **自动认证** - 移除手动auth命令，卡片靠近即自动认证  
✅ **智能冷却期** - 避免重复读取，支持快速换卡  
✅ **IRQ-based检测** - 低功耗，高响应性  
✅ **模块化架构** - 清晰的代码结构，易于维护和扩展  
✅ **状态机管理** - 可靠的状态转换和事件处理  

## 硬件要求

- ESP32开发板
- PN532 NFC模块
- LED指示灯
- 连接线

### 接线图

```
PN532    ESP32
------   -----
VCC   -> 3.3V
GND   -> GND
SDA   -> GPIO21 (SDA)
SCL   -> GPIO22 (SCL)
IRQ   -> GPIO34
RST   -> GPIO5

LED   -> GPIO2
```

## 快速开始

### 1. 编译和上传

```bash
# 编译项目
pio run

# 上传到ESP32
pio run --target upload

# 监控串口输出
pio device monitor
```

### 2. 系统初始化

上电后系统会自动：
- 初始化所有模块
- LED闪烁指示启动完成
- 开始自动检测卡片

### 3. 注册卡片

```
> reg
-- 将空白MIFARE Classic卡片靠近读卡器 --
Card registered successfully: 1A2B3C4D
```

### 4. 自动认证

直接将已注册的卡片靠近读卡器：
- ✅ 认证成功：LED快速闪烁3次
- ❌ 认证失败：LED慢速闪烁2次

## 命令参考

| 命令 | 功能 | 示例 |
|------|------|------|
| `reg` | 进入注册模式 | `reg` |
| `list` | 列出所有注册卡片 | `list` |
| `del <UID>` | 删除指定卡片 | `del 1A2B3C4D` |
| `clear` | 清空所有卡片 | `clear` |

## 系统状态

### LED指示说明

- **系统启动**: 渐进式闪烁3次
- **注册模式**: 长亮500ms
- **认证成功**: 快速闪烁3次 (100ms间隔)
- **认证失败**: 慢速闪烁2次 (300ms间隔)
- **注册成功**: 长亮1秒 + 快速闪烁5次
- **注册失败**: 快速闪烁10次 (50ms间隔)

### 状态机流程

```
等待卡片 → 检测到卡片 → 自动认证 → 冷却期(1秒) → 等待卡片
    ↓
注册模式 → 检测到卡片 → 注册处理 → 等待卡片
```

## 冷却期逻辑

为了避免重复读取同一张卡片，系统实现了智能冷却期：

1. **认证后立即进入冷却期**
   - 持续时间：1秒
   - 立即发起下次检测准备

2. **冷却期内的行为**
   - 同一张卡：延迟100ms重新检测，重置冷却计时器
   - 不同卡片：立即结束冷却期，进行认证
   - 超时：自动结束冷却期

3. **优势**
   - 同卡持续贴近不会重复认证
   - 换卡可以立即响应
   - 低功耗IRQ-based检测

## 项目结构

```
├── src/
│   ├── main.cpp              # 主程序
│   ├── authentication.cpp    # 认证模块
│   ├── actuator.cpp         # 执行器模块
│   ├── card_manager.cpp     # 卡片管理模块
│   └── state_machine.cpp    # 状态机模块
├── include/
│   ├── authentication.h
│   ├── actuator.h
│   ├── card_manager.h
│   └── state_machine.h
├── docs/
│   ├── architecture.md      # 架构文档
│   └── state_machine_usage.md
└── platformio.ini           # 项目配置
```

## 技术细节

### IRQ-based检测

使用PN532的IRQ引脚进行事件驱动检测：

```cpp
// 发起检测
nfc.startPassiveTargetIDDetection(PN532_MIFARE_ISO14443A);

// 监控IRQ引脚
if (digitalRead(PN532_IRQ) == LOW) {
    // 有卡片数据可读
    handleCardDetected();
}
```

### 模块化设计

每个模块都有明确的职责：

- **Authentication**: NFC通信和卡片认证
- **Actuator**: 硬件控制和用户反馈
- **CardManager**: 数据持久化和卡片管理
- **StateMachine**: 状态管理和事件协调

## 故障排除

### 常见问题

1. **PN532未检测到**
   - 检查I2C连接
   - 确认电源供应
   - 验证引脚配置

2. **卡片读取失败**
   - 确保使用MIFARE Classic卡片
   - 检查卡片是否损坏
   - 调整卡片与读卡器的距离

3. **认证失败**
   - 确认卡片已注册
   - 检查数据库文件完整性
   - 重新注册卡片

### 调试模式

启用详细日志输出：

```cpp
// 在main.cpp中添加
#define DEBUG_MODE 1
```

## 扩展开发

### 添加新的执行器

```cpp
// 在actuator.h中添加
void controlServo(int angle);

// 在actuator.cpp中实现
void Actuator::controlServo(int angle) {
    // 舵机控制逻辑
}
```

### 添加新的认证方式

```cpp
// 在authentication.h中添加
AuthResult authenticateFingerprint();

// 实现指纹认证逻辑
```

## 许可证

本项目采用MIT许可证。详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 更新日志

### v2.0.0 (当前版本)
- ✅ 重构为模块化架构
- ✅ 实现IRQ-based自动认证
- ✅ 添加智能冷却期逻辑
- ✅ 移除手动auth命令
- ✅ 改进LED指示系统

### v1.0.0
- 基础NFC门禁功能
- 手动命令认证
- 简单LED控制

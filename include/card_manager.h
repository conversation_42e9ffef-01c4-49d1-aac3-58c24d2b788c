#ifndef CARD_MANAGER_H
#define CARD_MANAGER_H

#include <Arduino.h>
#include <ArduinoJson.h>
#include <SPIFFS.h>

// 卡片信息结构
struct CardInfo {
  String uid;
  String key;
  bool valid;
};

// 卡片管理类
class CardManager {
private:
  StaticJsonDocument<1024> cardDatabase;
  const char* CARD_FILE = "/cards.json";

public:
  CardManager();
  
  // 初始化
  bool initialize();
  
  // 卡片查找
  bool findCardByUID(const String& uid, String& keyHex);
  bool isCardRegistered(const String& uid);
  CardInfo getCardInfo(const String& uid);
  
  // 卡片管理
  bool addCard(const String& uid, const String& keyHex);
  bool removeCard(const String& uid);
  int getCardCount();
  
  // 数据库操作
  void listAllCards();
  bool clearAllCards();
  
private:
  // 文件系统操作
  void saveCards();
  void loadCards();
};

#endif // CARD_MANAGER_H

#ifndef ACTUATOR_H
#define ACTUATOR_H

#include <Arduino.h>

// 执行器类 - 负责控制LED、舵机等输出设备
class Actuator {
private:
  int ledPin;
  bool ledState;

public:
  Actuator(int pin);
  
  // 初始化
  void initialize();
  
  // LED控制
  void blinkLED(int times = 3, int delayMs = 100);
  void setLED(bool state);
  void toggleLED();
  
  // 认证成功指示
  void indicateAuthSuccess();
  
  // 认证失败指示
  void indicateAuthFailure();
  
  // 注册成功指示
  void indicateRegistrationSuccess();
  
  // 注册失败指示
  void indicateRegistrationFailure();
  
  // 系统启动指示
  void indicateSystemStart();
  
  // 进入注册模式指示
  void indicateRegistrationMode();
};

#endif // ACTUATOR_H

#ifndef STATE_MACHINE_H
#define STATE_MACHINE_H

#include <Arduino.h>

// 系统状态枚举
enum SystemState {
  WAITING_FOR_CARD,    // 等待卡片状态
  COOLDOWN,            // 冷却期状态
  REGISTRATION         // 注册模式状态
};

// 状态机类
class StateMachine {
private:
  SystemState currentState;
  unsigned long cooldownStartTime;
  unsigned long detectionStartTime;
  String lastCardUID;
  bool irqPrev;
  bool irqCurr;
  bool registrationMode;
  
  static const unsigned long COOLDOWN_TIMEOUT = 1000;  // 1秒冷却超时
  static const unsigned long SAME_CARD_DELAY = 100;    // 同卡延迟100ms

public:
  StateMachine();
  
  // 状态管理
  void setState(SystemState newState);
  SystemState getState() const;
  
  // 状态机主循环
  void update();
  
  // 事件处理
  void onCardDetected(const String& uid);
  void onAuthenticationResult(bool success, const String& uid);
  void onRegistrationComplete();
  
  // 注册模式控制
  void enterRegistrationMode();
  void exitRegistrationMode();
  bool isInRegistrationMode() const;
  
  // IRQ处理
  void updateIRQ();
  bool isIRQTriggered() const;
  
  // 冷却期管理
  void startCooldown(const String& uid);
  bool isCooldownExpired() const;
  bool shouldBypassCooldown(const String& uid) const;
  
  // 检测管理
  void startDetection();
  bool isDetectionActive() const;
  unsigned long getDetectionDuration() const;
};

#endif // STATE_MACHINE_H

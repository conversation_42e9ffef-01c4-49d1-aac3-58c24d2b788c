# 门禁系统架构文档

## 概述

本系统已重构为模块化架构，实现了基于IRQ的自动NFC认证，具有智能冷却期管理功能。

## 系统架构

### 模块划分

1. **认证模块** (`authentication.h/cpp`)
   - 负责NFC卡片的读取、认证和注册
   - 使用IRQ-based检测，提高功耗效率
   - 支持MIFARE Classic卡片

2. **执行器模块** (`actuator.h/cpp`)
   - 控制LED指示灯
   - 提供不同状态的视觉反馈
   - 后期可扩展支持舵机等执行器

3. **状态机模块** (`state_machine.h/cpp`)
   - 管理系统状态转换
   - 实现冷却期逻辑
   - 处理IRQ事件

4. **卡片管理模块** (`card_manager.h/cpp`)
   - 管理卡片数据库
   - 处理SPIFFS文件系统操作
   - 提供卡片增删查改功能

5. **主程序** (`main.cpp`)
   - 系统初始化
   - 串口命令处理
   - 模块协调

## 状态机设计

### 状态类型

- **WAITING_FOR_CARD**: 等待卡片状态
- **COOLDOWN**: 冷却期状态
- **REGISTRATION**: 注册模式状态

### 状态转换

```
WAITING_FOR_CARD → [卡片检测] → 认证处理 → COOLDOWN
COOLDOWN → [超时1秒] → WAITING_FOR_CARD
COOLDOWN → [不同卡片] → 立即认证 → COOLDOWN
COOLDOWN → [相同卡片] → 延迟100ms重新检测
WAITING_FOR_CARD → [注册命令] → REGISTRATION
REGISTRATION → [注册完成] → WAITING_FOR_CARD
```

## 冷却期逻辑

### 设计目标
- 避免同一张卡重复认证
- 允许换卡立即响应
- 使用IRQ-based检测提高效率

### 实现细节

1. **认证后进入冷却期**
   - 立即发起`startPassiveTargetIDDetection`
   - 记录发起时间和卡片UID
   - 持续监控IRQ引脚

2. **冷却期超时处理**
   - 1秒后自动结束冷却期
   - 返回等待卡片状态
   - 注意：检测已在冷却期发起，无需重复发起

3. **冷却期IRQ处理**
   - 相同UID：延迟100ms后重新发起检测，重置冷却计时器
   - 不同UID：立即结束冷却期，进行认证

## 命令变更

### 移除的命令
- `auth` - 认证现在是自动的

### 保留的命令
- `reg` - 进入注册模式
- `list` - 列出所有注册卡片
- `del <UID>` - 删除指定卡片

### 新增的命令
- `clear` - 清空所有注册卡片

## 使用方法

1. **系统启动**
   - 上电后自动初始化所有模块
   - LED指示系统启动完成
   - 自动开始卡片检测

2. **卡片注册**
   ```
   reg
   [将空白卡片靠近读卡器]
   ```

3. **卡片认证**
   - 直接将已注册卡片靠近读卡器
   - 系统自动进行认证
   - LED指示认证结果

4. **卡片管理**
   ```
   list          # 查看所有卡片
   del 1A2B3C4D  # 删除指定UID的卡片
   clear         # 清空所有卡片
   ```

## 技术特性

### IRQ-based检测
- 使用`startPassiveTargetIDDetection`发起检测
- 监控IRQ引脚状态变化
- 避免轮询模式，降低功耗

### 智能冷却期
- 防止重复读取同一张卡
- 支持快速换卡响应
- 可配置的超时和延迟参数

### 模块化设计
- 清晰的职责分离
- 易于扩展和维护
- 支持单元测试

## 配置参数

```cpp
// 状态机参数
COOLDOWN_TIMEOUT = 1000ms     // 冷却期超时
SAME_CARD_DELAY = 100ms       // 同卡检测延迟

// 硬件引脚
PN532_IRQ = 34               // IRQ引脚
PN532_RESET = 5              // 复位引脚
LED_PIN = 2                  // LED引脚
```

## 扩展建议

1. **执行器扩展**
   - 添加舵机控制
   - 支持蜂鸣器提示
   - 增加继电器控制

2. **认证方式扩展**
   - 支持指纹识别
   - 添加密码认证
   - 实现多因子认证

3. **网络功能**
   - WiFi连接
   - 远程管理
   - 日志上传
